{"type": "project", "license": "proprietary", "minimum-stability": "dev", "prefer-stable": true, "require": {"php": ">=8.1", "ext-ctype": "*", "ext-iconv": "*", "amphp/http-client": "^4.2.1", "doctrine/annotations": "^2.0", "doctrine/mongodb-odm": "^2.6", "doctrine/mongodb-odm-bundle": "^5.3", "friendsofsymfony/rest-bundle": "^3.7", "jms/serializer-bundle": "^5.5", "mongodb/mongodb": "^2.0", "nelmio/api-doc-bundle": "^4.12", "phpdocumentor/reflection-docblock": "^5.3", "phpstan/phpdoc-parser": "^1.20", "space/mongo-documents": "*", "symfony/amazon-sqs-messenger": "6.4.*", "symfony/asset": "6.4.*", "symfony/console": "6.4.*", "symfony/dotenv": "6.4.*", "symfony/flex": "^2", "symfony/framework-bundle": "6.4.*", "symfony/http-client": "6.4.*", "symfony/intl": "6.4.*", "symfony/monolog-bundle": "^3.10", "symfony/property-access": "6.4.*", "symfony/runtime": "6.4.*", "symfony/serializer": "6.4.*", "symfony/twig-bundle": "6.4.*", "symfony/validator": "6.4.*", "symfony/yaml": "6.4.*", "webmozart/assert": "^1.11"}, "repositories": [{"type": "path", "url": "../../space-mongo-documents"}], "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "symfony/flex": true, "symfony/runtime": true}, "optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "6.4.*"}}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.51", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.5", "rector/rector": "^1.0", "symfony/browser-kit": "6.4.*", "symfony/css-selector": "6.4.*", "symfony/phpunit-bridge": "^7.0", "symfony/web-profiler-bundle": "6.4.*"}}