{"doctrine/annotations": {"version": "2.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.10", "ref": "64d8583af5ea57b7afa4aba4b159907f3a148b05"}}, "doctrine/deprecations": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "87424683adc81d7dc305eefec1fced883084aab9"}}, "doctrine/mongodb-odm-bundle": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "5.0", "ref": "873fc0ba74e93e1b9712b732b7d580e418558d2e"}, "files": ["config/packages/doctrine_mongodb.yaml", "src/Document/.gitignore"]}, "friendsofphp/php-cs-fixer": {"version": "3.51", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "be2103eb4a20942e28a6dd87736669b757132435"}, "files": [".php-cs-fixer.dist.php"]}, "friendsofsymfony/rest-bundle": {"version": "3.7", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.0", "ref": "3762cc4e4f2d6faabeca5a151b41c8c791bd96e5"}, "files": ["config/packages/fos_rest.yaml"]}, "jms/serializer-bundle": {"version": "5.5", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "4.0", "ref": "cc04e10cf7171525b50c18b36004edf64cb478be"}, "files": ["config/packages/jms_serializer.yaml"]}, "nelmio/api-doc-bundle": {"version": "4.12", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.0", "ref": "c8e0c38e1a280ab9e37587a8fa32b251d5bc1c94"}, "files": ["config/packages/nelmio_api_doc.yaml", "config/routes/nelmio_api_doc.yaml"]}, "phpstan/phpstan": {"version": "1.10", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "5e490cc197fb6bb1ae22e5abbc531ddc633b6767"}, "files": ["phpstan.dist.neon"]}, "phpunit/phpunit": {"version": "9.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "9.6", "ref": "7364a21d87e658eb363c5020c072ecfdc12e2326"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/console": {"version": "6.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "da0c8be8157600ad34f10ff0c9cc91232522e047"}, "files": ["./bin/console"]}, "symfony/flex": {"version": "2.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "146251ae39e06a95be0fe3d13c807bcf3938b172"}, "files": ["./.env"]}, "symfony/framework-bundle": {"version": "6.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.4", "ref": "3cd216a4d007b78d8554d44a5b1c0a446dab24fb"}, "files": ["./config/packages/cache.yaml", "./config/packages/framework.yaml", "./config/preload.php", "./config/routes/framework.yaml", "./config/services.yaml", "./public/index.php", "./src/Controller/.gitignore", "./src/Kernel.php"]}, "symfony/messenger": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.0", "ref": "ba1ac4e919baba5644d31b57a3284d6ba12d52ee"}, "files": ["config/packages/messenger.yaml"]}, "symfony/monolog-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "aff23899c4440dd995907613c1dd709b6f59503f"}, "files": ["config/packages/monolog.yaml"]}, "symfony/phpunit-bridge": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "a411a0480041243d97382cac7984f7dce7813c08"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/routing": {"version": "6.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.1", "ref": "a44010c0d06989bd4f154aa07d2542d47caf5b83"}, "files": ["./config/packages/routing.yaml", "./config/routes.yaml"]}, "symfony/twig-bundle": {"version": "6.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.4", "ref": "bb2178c57eee79e6be0b297aa96fc0c0def81387"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/validator": {"version": "6.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "c32cfd98f714894c4f128bb99aa2530c1227603c"}, "files": ["config/packages/validator.yaml"]}, "symfony/web-profiler-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.1", "ref": "e42b3f0177df239add25373083a564e5ead4e13a"}, "files": ["config/packages/web_profiler.yaml", "config/routes/web_profiler.yaml"]}}