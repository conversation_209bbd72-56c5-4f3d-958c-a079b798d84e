<?php

namespace App\Manager;

use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use Webmozart\Assert\Assert;

use App\Helper\VehicleTypeEntities;
use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Helper\BrandHelper;
use App\Model\CatalogModel;
use App\Connector\SysSamsDataConnector;
use App\Service\ContribService;
use App\Trait\LoggerTrait;
use App\Trait\ValidationResponseTrait;
use App\Manager\UserManager;
use App\Service\CorvetService;
use App\DataMapper\CatalogDataMapper;

/**
 * Catalog Manager.
 */
class CatalogManager
{
    use LoggerTrait;
    use ValidationResponseTrait;

    const SOURCE_APP = 'APP';
    const BUNDLE = 'BUNDLE';
    const LEV = "LEV";

    const APP_GROUP_NAMES = [
        self::BUNDLE => 'bundle'
    ];

    public function __construct(private SysSamsDataConnector $sysSamsDataConnector, private SerializerInterface $serializer, private ContribService $contribService, private ValidatorInterface $validator, private UserManager $userManager, private CorvetService $corvetService)
    {
    }

    private function getCorvetData(
        string $vin,
        string $brand,
        string $country
    ): ?array {
        return $this->corvetService->getCorvetResponse($brand, $country, $vin, self::SOURCE_APP);
    }


    public function getCategory(?string $familyName): string
    {
        $connectedServices = ['NAVCOZAR', 'TMTS', 'NAVCO', 'ZAR', 'LEV', 'PHEV', 'BEV', 'RACCESS', 'CONNECTEDALARM', 'DIGITALKEY', 'AE_CALL', 'EV_ROUTING_APP', 'PARTNERSERVICE', 'TRIPS_IN_THE_CLOUD', 'STOLEN_VEHICLE'];
        $afterSalesServices = ['DIMBO', 'PRIVILEGE'];
        if (in_array($familyName, $connectedServices)) {
            return 'CONNECTED_SERVICES';
        } elseif (in_array($familyName, $afterSalesServices)) {
            return 'AFTERSALES_SERVICES';
        } else {
            return 'OTHERS';
        }
    }

    private function _checkVehicleHorE($brand, $country, $vin, $source)
    {
        $response = $this->getCorvetData($vin, $brand, $country);
        if (!isset($response['error'])) {
            $allAttributes = $response['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
            $corvertAttributs = $this->corvetService->getManagedAttributes($allAttributes);
            $vehicleTypeNumber = VehicleTypeEntities::getType('DXD', $corvertAttributs);
            return in_array($vehicleTypeNumber, ['02', '03', '04', '05', '06']);
        }
        return false;
    }


    protected static function extractCatalogueGroupName(array $item): ?string
    {        
        if (strtoupper($item['type']) === self::BUNDLE && !empty($item['standaloneProducts']))
            return self::BUNDLE;

        return null;
    }

    /**
     * get Catalog data.
     */
    public function getCatalog(array $params): ResponseArrayFormat
    {
        $this->logger->info('=> Call Catalog API : ' . __METHOD__ . ' with parameters : ', $params);
        try {
            $user = $this->userManager->getUserByUserId($params['userId']);
            if (!$user) {
                $this->logger->error('No user found', ['userId' => $params['userId']]);
                return new ErrorResponse('No user found', Response::HTTP_NOT_FOUND);
            }

            $userDbId = $user->getUserDbId();
            if (empty($userDbId)) {
                $this->logger->error('No user DB ID found for', ['userId' => $params['userId']]);
                return new ErrorResponse('No user DB ID found', Response::HTTP_NOT_FOUND);
            }

            $params['userDbId'] = $userDbId;

            // Call catalog API directly via SysSamsDataConnector
            $options = [
                'query' => [
                    'brand' => $params['brand'],
                    'country' => $params['country'],
                    'language' => $params['language'],
                ],
                'headers' => [
                    'userId' => $params['userDbId'],
                    'vin' => $params['vin'],
                ],
            ];
            $url = '/v1/catalog';
            $this->logger->info('=> '.__METHOD__." => Call API [$url] with options ", ['options' => $options]);

            $catalogResponse = $this->sysSamsDataConnector->call(Request::METHOD_GET, $url, $options);
            if (Response::HTTP_OK == $catalogResponse->getCode()) {
                $responseData = $catalogResponse->getData();
                if (!is_array($responseData)) {
                    $this->logger->error('Catalog API returned non-array response', [
                        'response_type' => gettype($responseData),
                        'response_data' => $responseData
                    ]);
                    return new ErrorResponse('External catalog service returned invalid response format', Response::HTTP_BAD_GATEWAY);
                }

                if (!isset($responseData['success'])) {
                    $this->logger->error('Catalog API response missing success key', ['response_data' => $responseData]);
                    return new ErrorResponse('External catalog service returned invalid response structure', Response::HTTP_BAD_GATEWAY);
                }
                $catalogResponseData = $responseData['success'];
                $catalogDataList = [];
                foreach ($catalogResponseData as $catalogData) {
                    $productId = $catalogData['id'] ?? '';
                    $brand = $params['brand'] ?? '';
                    $vin = $params['vin'] ?? '';
                    $cultureCode = $params['language'] . '-' . $params['country'];
                    $isElectricOrHybrid = $this->_checkVehicleHorE($params['brand'], $params['country'], $vin, $params['source']);
                    $groupName = static::extractCatalogueGroupName($catalogData);
                    if (($groupName != self::BUNDLE) || ($groupName === self::LEV && !$isElectricOrHybrid)) {
                        continue;
                    }
                    $contribResponse = $this->contribService->getContrib($brand, $cultureCode, $productId, self::SOURCE_APP);
                    if ($contribResponse->getCode() !== Response::HTTP_OK) {
                        $statusCode = $contribResponse->getCode();
                        $contribResponse = $contribResponse->getData();
                        $result = (isset($contribResponse['error']['message'])) ? $contribResponse['error']['message'] : $contribResponse;
                        $this->logger->error('=> ' . __METHOD__ . ' => error : ' . $result);
                        return new ErrorResponse($result, $statusCode);
                    }
                    $contribData = $contribResponse->getData()['success'];
                    if (is_array($contribData) && isset($contribData[$productId])) {
                        $catalog = (new CatalogDataMapper)->setItem($catalogData)->transform($contribData[$productId]);
                        $category = $this->getCategory($groupName);
                        $catalog['type'] = $groupName;
                        $catalog['category'] = strtolower($catalogData['type']) == strtolower(self::BUNDLE) ?  strtolower($this->getCategory(self::BUNDLE)) : $category;
                        $catalog['id'] = strtolower($catalogData['type']) == strtolower(self::BUNDLE) ? strtolower($catalogData['groupName']) :self::APP_GROUP_NAMES[$groupName] ?? '' ;
                        $catalogDataList[] = $catalog;
                    }
                }

                return new SuccessResponse($catalogDataList);
            } else {
                // Handle non-200 responses
                $responseData = $catalogResponse->getData();
                $errorMessage = 'External catalog service error';

                if (is_array($responseData) && isset($responseData['error']['message'])) {
                    $errorMessage = $responseData['error']['message'];
                } elseif (is_string($responseData)) {
                    $errorMessage = $responseData;
                }

                $this->logger->error('=> ' . __METHOD__ . ' => external API error', [
                    'status_code' => $catalogResponse->getCode(),
                    'response_data' => $responseData,
                    'error_message' => $errorMessage
                ]);

                return new ErrorResponse($errorMessage, $catalogResponse->getCode() ?: Response::HTTP_BAD_GATEWAY);
            }
        } catch (\Exception $e) {
            $this->logger->error('=> ' . __METHOD__ . 'Catched Exception CatalogManager::getCatalog ' . $e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }
}
