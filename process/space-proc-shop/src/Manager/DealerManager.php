<?php

namespace App\Manager;

use App\DataMapper\DealerDataMapper;
use App\DtoRequest\XfEmeaParameterRequest;
use App\DtoResponse\DealerResponseDto;
use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Service\DealerService;
use App\Service\SettingsService;
use App\Trait\LoggerTrait;
use App\Transformer\XfEmeaResponseTransformer;
use App\Transformer\XpResponseTransformer;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Dealer Manager.
 */
class DealerManager
{
    use LoggerTrait;

    public function __construct(
        private DealerService $service,
        private SerializerInterface $serializer,
        private DealerDataMapper $dealerDataMapper,
        private SettingsService $settingsService
    ) {
    }

    /**
     * get Dealer data.
     */
    public function getDealerList($params): ResponseArrayFormat{
        try {
            $dealer = null;
            $params['o2x'] = $this->getO2xSettings($params['brand'], $params['source']);
            $response = $this->callDealerList($params);
            if ($response['code'] != 200) {
                return new ErrorResponse(
                    $response ['data'] ?? 'Dealers not found',
                    $response['code']
                );
            }
            $data = $response['data']['success'] ?? [];
            if ($data) {
                $dealer = $this->getMappedDealerData($data, $params);
            }
            if(isset($params['model']) && !empty($params['model'])){
                if (strtoupper($params['model']) == 'O2X') {
                    $params['brand'] = 'XX';
                    $xxBrandResponse = $this->callDealerList($params);
                    if ($xxBrandResponse['code'] != 200) {
                        return new ErrorResponse(
                            $xxBrandResponse ['data'] ?? 'Dealers not found',
                            $xxBrandResponse['code']
                        );
                    }
                    $data = $xxBrandResponse['data']['success'] ?? [];
                    $dealer = $this->getMappedDealerData($data, $params, true, $dealer);
                }else{
                    return new SuccessResponse([]);
                }
            }
            return new SuccessResponse($dealer->getSuccess());            
        } catch (\Exception $e) {
            $this->logger->error('Error => ' . __METHOD__ . ' Catched Exception ' . $e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    /**
     * get Xf Emea Dealer data.
     */
    public function getDealerXfEmeaList(XfEmeaParameterRequest $params): ResponseArrayFormat
    {
        $this->logger->info('=> Call Dealer for xf Emea API : '.__METHOD__.' with parameters : ', (array)$params);
        try {
            $response = $this->service->getXfEmeaDealerList($params);
            if(isset($response->getData()['error'])) {
                return new ErrorResponse(
                    $response->getData()['error']['message'] ?? '',
                    $response->getCode()
                );
            }
            $xfEmeaResponse = XfEmeaResponseTransformer::mapper(
                $response->getData()['success'] ?? [],
                $params
            );

            return new SuccessResponse($xfEmeaResponse->getSuccess());
        } catch (\Exception $e) {
            $this->logger->error('=> '.__METHOD__.' Catched Exception DealerManager::getDealerList for xf Emea '.$e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    private function getO2xSettings(string $brand, $source = "APP"): array
    {
        try {
            // Use ODM service to get O2X settings
            $o2xSettings = $this->settingsService->getO2xSettings($brand, '', '');

            if (!$o2xSettings) {
                $this->logger->warning('O2X settings not found', [
                    'brand' => $brand,
                    'source' => $source
                ]);
                return [];
            }

            // Handle APP source specific logic
            if ($source === 'APP') {
                // Look for config-based o2x settings first
                $settingsData = $o2xSettings['settingsData'] ?? [];
                if (!empty($settingsData)) {
                    $configBasedData = current(array_filter($settingsData, function($item) {
                        $code = $item['config']['code'] ?? '';
                        return $code === 'o2x';
                    }));

                    if ($configBasedData) {
                        $config = $configBasedData['config'] ?? [];
                        $result = array_merge($configBasedData, $config);
                        unset($result['config']);
                        return $result;
                    }
                }

                // Fallback to direct o2x data
                return $o2xSettings['o2x'] ?? $o2xSettings;
            }

            // For non-APP sources, return o2x data directly
            return $o2xSettings['o2x'] ?? $o2xSettings;
        } catch (\Exception $e) {
            $this->logger->error(
                sprintf('%s: Error while getting o2x settings ', __METHOD__),
                [
                    'brand' => $brand,
                    'source' => $source,
                    'exception_code' => $e->getCode(),
                    'exception_message' => $e->getMessage(),
                ]
            );
            return [];
        }
    }

    private function getMappedDealerData(array $data, array $params, bool $withXXCall = false, ?DealerResponseDto $dealerData = null): DealerResponseDto{
        return XpResponseTransformer::mapper(
            $data ?? [],
            $params,
            $withXXCall,
            $dealerData
        );
        
    }

    private function callDealerList(array $params): ?array{
        $this->logger->info('=> Call Dealer API with : '.__METHOD__.' with parameters : ', $params);
        $response = $this->service->getDealerList($params);
        return [
            'data' => $response->getData() ?? null,
            'code' => $response->getCode()
        ];
    }
}
