<?php

namespace App\Manager;

use App\Trait\LoggerTrait;
use App\Service\UserDataService;
use App\Helper\WSResponse;
use Symfony\Component\HttpFoundation\Response;

class UserManager
{
    use LoggerTrait;
    public function __construct(
        private UserDataService $userDataService
    ) {
    }

    public function getUsersBySessionId(?string $userId, ?string $vin, ?string $sessionId)
    {
        $this->logger->info('Getting users', ['userId' => $userId, 'vin' => $vin, 'sessionId' => $sessionId]);
        $users = $this->userDataService->getUsersBySessionId($sessionId, $userId, $vin);
        return $users;
    }

    public function getUserByUserId($userId)
    {
        $this->logger->info('Getting user', ['userId' => $userId]);
        $user = $this->userDataService->getUserByUserId($userId);
        return $user;
    }

    public function updateVehicleFeatureCode($userId, $vin, $featureCode, $featureStatus): WSResponse
    {
        $this->logger->info('Updating user vehicle for feature code and status', ['userId' => $userId, 'vin' => $vin]);
        $featureCodeData = [
            'code' => $featureCode,
            'status' => $featureStatus
        ];

        try {
            $result = $this->userDataService->updateVehicleFeatureCode($userId, $vin, $featureCodeData);

            if ($result) {
                return new WSResponse(Response::HTTP_OK, json_encode(['success' => true]));
            } else {
                return new WSResponse(Response::HTTP_BAD_REQUEST, json_encode(['error' => 'Failed to update feature code']));
            }
        } catch (\Exception $e) {
            $this->logger->error('Error updating vehicle feature code', [
                'userId' => $userId,
                'vin' => $vin,
                'featureCode' => $featureCode,
                'featureStatus' => $featureStatus,
                'error' => $e->getMessage()
            ]);
            return new WSResponse(Response::HTTP_SERVICE_UNAVAILABLE, json_encode(['error' => $e->getMessage()]));
        }
    }
}