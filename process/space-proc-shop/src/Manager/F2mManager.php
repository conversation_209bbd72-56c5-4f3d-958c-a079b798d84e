<?php

namespace App\Manager;

use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Model\ChargeStationAddressModel;
use App\Model\ChargeStationModel;
use App\Model\CoordinateModel;
use App\Model\POIModel;
use App\Service\F2mService;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;
use Webmozart\Assert\Assert;

/**
 * F2mManager
 */
class F2mManager 
{
    use LoggerTrait;

    public function __construct(private F2mService $f2mService)
    {
    }

    /**
     * get charging stationg data
     */
    public function search(array $params)
    {
        try {
            $this->logger->info('Start of calling F2m charging station locator');
            $response = $this->f2mService->search($params);
            $this->logger->info('End F2m charging station locator');

            if (Response::HTTP_OK == $response->getCode()) {
                $brand = $params['brand'];
                $responseData = $response->getData();

                if (!is_array($responseData)) {
                    $this->logger->error('F2M API returned non-array response', [
                        'response_type' => gettype($responseData),
                        'response_data' => $responseData
                    ]);
                    return new ErrorResponse('External F2M service returned invalid response format', Response::HTTP_BAD_GATEWAY);
                }

                if (!isset($responseData['success'])) {
                    $this->logger->error('F2M API response missing success key', ['response_data' => $responseData]);
                    return new ErrorResponse('External F2M service returned invalid response structure', Response::HTTP_BAD_GATEWAY);
                }

                $chargingStations = $responseData['success'] ?? [];
                $data = array_map(function ($responseData) use ($brand) {
                    return $this->mapChargingStationData($responseData, $brand);
                }, $chargingStations);
                return (new SuccessResponse($data));
            } else {
                $responseData = $response->getData();
                $errorMessage = 'External F2M service error';

                if (is_array($responseData) && isset($responseData['error']['message'])) {
                    $errorMessage = $responseData['error']['message'];
                } elseif (is_string($responseData)) {
                    $errorMessage = $responseData;
                }

                $this->logger->error('F2M API error', [
                    'status_code' => $response->getCode(),
                    'response_data' => $responseData,
                    'error_message' => $errorMessage
                ]);

                // Ensure we have a valid HTTP status code
                $statusCode = $response->getCode();
                if ($statusCode < 100 || $statusCode > 599) {
                    $statusCode = Response::HTTP_BAD_GATEWAY;
                }

                return new ErrorResponse($errorMessage, $statusCode);
            }
        } catch (\Exception $e) {
            $this->logger->error('Manager :: Error of getting F2m charging station locator ' . $e->getMessage() . '. Code error :' . Response::HTTP_BAD_REQUEST);

            // Ensure we have a valid HTTP status code
            $statusCode = $e->getCode();
            if ($statusCode < 100 || $statusCode > 599) {
                $statusCode = Response::HTTP_SERVICE_UNAVAILABLE;
            }

            return new ErrorResponse("Error getting a response from F2M charging station locator", $statusCode);
        }
    }

    private function isValidResponse(array $response): bool
    {
        return isset($response['content']) && $response['code'] && $response['code'] == Response::HTTP_OK;
    }

    private function mapChargingStationData(array $responseData, string $brand): ChargeStationModel
    {
        $chargingStationg = new ChargeStationModel();
        $chargingStationg->setId($responseData['charging_station_id']);
        $chargingStationg->setChargeStationId($responseData['charging_station_id']);
        $chargingStationg->setLocationId($responseData['location_id']);
        $chargingStationg->setPoi($this->mapPoiData());
        $chargingStationg->setAddress($this->mapAddressData());
        $chargingStationg->setPosition($this->mapPosition($responseData['coordinates']));
        $chargingStationg->setConnectors($this->mapConnectorsData());
        $chargingStationg->setCslProviderData(['f2m' => null]);
        $chargingStationg->setBrand($brand);

        return $chargingStationg;
    }

    private function mapPoiData(): POIModel
    {
        return new POIModel();
    }

    private function mapPosition($responseData): CoordinateModel
    {
        $position = new CoordinateModel();
        $position->setLatitude($responseData['latitude']);
        $position->setLongitude($responseData['longitude']);
        return $position;
    }

    private function mapAddressData(): ChargeStationAddressModel
    {
        return new ChargeStationAddressModel();
    }

    private function mapConnectorsData(): array
    {
        return [
            [
                'type' => null,
                'compatible' => true,
                'powerLevel' => [],
                'total' => 0,
                'availability' => [],
            ]
        ];
    }
}
