<?php

namespace App\Connector;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;

/**
 * Kind of HttpClientInterface decorator.
 */
class CustomHttpClient
{
    use LoggerTrait;

    public function __construct(private InternalHttpClientService $client)
    {
    }

    public function request(string $method, string $url, array $options = []): WSResponse
    {
        try {
            // Log request details
            $this->logger->info("REQUEST: {$method} {$url}", [
                'method' => $method,
                'url' => $url,
                'options' => json_encode($options)
            ]);
            
            $response = $this->client->request($method, $url, $options);
            $statusCode = $response->getStatusCode();

            // Handle response data parsing with error handling
            $data = [];
            if (Response::HTTP_NO_CONTENT != $statusCode) {
                try {
                    $data = $response->toArray(false);
                } catch (\Exception $e) {
                    // If JSON parsing fails, get the raw content
                    $rawContent = $response->getContent(false);
                    $this->logger->warning("Failed to parse JSON response", [
                        'method' => $method,
                        'url' => $url,
                        'status_code' => $statusCode,
                        'raw_content' => substr($rawContent, 0, 500), // Log first 500 chars
                        'parse_error' => $e->getMessage()
                    ]);

                    // Return a structured error response
                    $data = [
                        'error' => [
                            'message' => 'Invalid response format from external service',
                            'details' => $e->getMessage(),
                            'raw_content' => $rawContent
                        ]
                    ];
                }
            }

            // Log response details
            $this->logger->info("RESPONSE: {$method} {$url}", [
                'status_code' => $statusCode,
                'data' => is_array($data) ? json_encode($data) : $data
            ]);

            $response = new WSResponse($statusCode, $data);

            return $response;
        } catch (\Exception $e) {
            $this->logger->error("Cached Exception : CustomHttpClient::request " . $e->getMessage(), [
                'method' => $method,
                'url' => $url,
                'error_code' => $e->getCode(),
                'trace' => $e->getTraceAsString()
            ]);

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }
}
