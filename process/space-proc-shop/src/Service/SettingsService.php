<?php

namespace App\Service;

use Space\MongoDocuments\Repository\SettingsRepository;
use Space\MongoDocuments\Document\Settings;
use App\Trait\LoggerTrait;
use RuntimeException;
use Symfony\Component\HttpFoundation\Response;
use Doctrine\ODM\MongoDB\DocumentManager;

class SettingsService
{
    use LoggerTrait;

    public function __construct(
        private SettingsRepository $settingsRepository,
        private DocumentManager $documentManager
    ) {
    }

    /**
     * Get O2X settings (for DealerManager)
     */
    public function getO2xSettings(string $brand, string $country, string $culture): ?array
    {
        try {
            $filter = [
                'brand' => $brand,
                'culture' => $culture,
                '$or' => [
                    ['settingsData.o2x.code' => $country],
                    ['settingsData.config.code' => $country]
                ]
            ];

            $settings = $this->settingsRepository->findByComplexFilter($filter);
            return $settings?->getSettingsData();
        } catch (\Exception $e) {
            $this->logger->error('Error getting O2X settings', [
                'brand' => $brand,
                'country' => $country,
                'culture' => $culture,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to get O2X settings', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Find settings by type, brand, country, and culture
     */
    public function findByTypeAndBrandAndCountryAndCulture(string $type, string $brand, string $country, string $culture): ?Settings
    {
        try {
            return $this->settingsRepository->findOneBy([
                'type' => $type,
                'brand' => $brand,
                'country' => $country,
                'culture' => $culture
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Error finding settings', [
                'type' => $type,
                'brand' => $brand,
                'country' => $country,
                'culture' => $culture,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find settings', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Find settings by type and brand
     */
    public function findByTypeAndBrand(string $type, string $brand): array
    {
        try {
            return $this->settingsRepository->findByTypeAndBrand($type, $brand);
        } catch (\Exception $e) {
            $this->logger->error('Error finding settings by type and brand', [
                'type' => $type,
                'brand' => $brand,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find settings by type and brand', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Find settings by type
     */
    public function findByType(string $type): array
    {
        try {
            return $this->settingsRepository->findByType($type);
        } catch (\Exception $e) {
            $this->logger->error('Error finding settings by type', [
                'type' => $type,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find settings by type', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Save settings
     */
    public function save(Settings $settings): void
    {
        try {
            $this->documentManager->persist($settings);
            $this->documentManager->flush();
        } catch (\Exception $e) {
            $this->logger->error('Error saving settings', [
                'type' => $settings->getType(),
                'brand' => $settings->getBrand(),
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to save settings', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Create new settings
     */
    public function create(string $type, string $brand, string $country, string $culture): Settings
    {
        try {
            $settings = new Settings();
            $settings->setType($type);
            $settings->setBrand($brand);
            $settings->setCountry($country);
            $settings->setCulture($culture);
            return $settings;
        } catch (\Exception $e) {
            $this->logger->error('Error creating settings', [
                'type' => $type,
                'brand' => $brand,
                'country' => $country,
                'culture' => $culture,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to create settings', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Update settings data
     */
    public function updateData(Settings $settings, array $data): void
    {
        try {
            $settings->setData($data);
            $this->documentManager->persist($settings);
            $this->documentManager->flush();
        } catch (\Exception $e) {
            $this->logger->error('Error updating settings data', [
                'type' => $settings->getType(),
                'brand' => $settings->getBrand(),
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to update settings data', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Update settings data by key-value
     */
    public function updateSettingsData(Settings $settings, array $settingsData): void
    {
        try {
            $settings->setSettingsData($settingsData);
            $this->documentManager->persist($settings);
            $this->documentManager->flush();
        } catch (\Exception $e) {
            $this->logger->error('Error updating settings data', [
                'type' => $settings->getType(),
                'brand' => $settings->getBrand(),
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to update settings data', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }
}
