<?php

namespace App\Service;

use Space\MongoDocuments\Service\MongoDBService;
use Space\MongoDocuments\Document\UserData;
use Space\MongoDocuments\Document\Vehicle;
use App\Trait\LoggerTrait;
use RuntimeException;
use Symfony\Component\HttpFoundation\Response;

class UserDataService
{
    use LoggerTrait;

    public function __construct(
        private MongoDBService $mongoDBService
    ) {
    }

    /**
     * Get users by session ID (complex aggregation)
     * This implements the complex aggregation pipeline that was previously in UserService
     */
    public function getUsersBySessionId(string $sessionId, string $userId = null, string $vin = null): array
    {
        try {
            $this->logger->info('Getting users by session ID', [
                'sessionId' => $sessionId,
                'userId' => $userId,
                'vin' => $vin
            ]);

            // Build the aggregation pipeline equivalent to the old UserService logic
            $pipeline = [
                [
                    '$match' => [
                        'userId' => $userId,
                        'vehicle.vin' => $vin
                    ]
                ],
                ['$unwind' => '$push'],
                [
                    '$match' => [
                        'push.commandSession.remoteSessionId' => $sessionId,
                    ],
                ],
                [
                    '$group' => [
                        '_id' => 'pushDetails',
                        'push' => [
                            '$push' => '$push',
                        ],
                    ],
                ],
            ];

            // Use the MongoDBService to execute the aggregation
            $result = $this->mongoDBService->aggregate('UserData', $pipeline);

            return $result ?? [];
        } catch (\Exception $e) {
            $this->logger->error('Error getting users by session ID', [
                'sessionId' => $sessionId,
                'userId' => $userId,
                'vin' => $vin,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to get users by session ID', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Get user by user ID
     */
    public function getUserByUserId(string $userId): ?UserData
    {
        try {
            return $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);
        } catch (\Exception $e) {
            $this->logger->error('Error getting user by user ID', [
                'userId' => $userId,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to get user by user ID', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Update vehicle feature code
     */
    public function updateVehicleFeatureCode(string $userId, string $vin, array $featureCodeData): bool
    {
        try {
            $userData = $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);
            if (!$userData) {
                return false;
            }

            // Find and update the vehicle with the specified VIN
            $vehicles = $userData->getVehicles();
            foreach ($vehicles as $vehicle) {
                if ($vehicle->getVin() === $vin) {
                    $vehicle->setFeatureCode($featureCodeData);
                    $this->mongoDBService->save($userData);
                    return true;
                }
            }
            return false;
        } catch (\Exception $e) {
            $this->logger->error('Error updating vehicle feature code', [
                'userId' => $userId,
                'vin' => $vin,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to update vehicle feature code', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Find vehicle by VIN
     */
    public function findVehicleByVin(string $vin): ?array
    {
        try {
            $userDataList = $this->mongoDBService->findBy(UserData::class, []);
            foreach ($userDataList as $userData) {
                $vehicles = $userData->getVehicles();
                foreach ($vehicles as $vehicle) {
                    if ($vehicle->getVin() === $vin) {
                        return [
                            'userId' => $userData->getUserId(),
                            'vehicle' => $vehicle
                        ];
                    }
                }
            }
            return null;
        } catch (\Exception $e) {
            $this->logger->error('Error finding vehicle by VIN', [
                'vin' => $vin,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find vehicle by VIN', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Get LCDV by VIN (for EvRoutingService)
     */
    public function getLcdvByVin(string $vin): ?string
    {
        try {
            $vehicleData = $this->findVehicleByVin($vin);
            if ($vehicleData && isset($vehicleData['vehicle'])) {
                $vehicle = $vehicleData['vehicle'];
                return $vehicle->getVersionId();
            }
            return null;
        } catch (\Exception $e) {
            $this->logger->error('Error getting LCDV by VIN', [
                'vin' => $vin,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to get LCDV by VIN', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Save user data
     */
    public function save(UserData $userData): void
    {
        try {
            $this->mongoDBService->save($userData);
        } catch (\Exception $e) {
            $this->logger->error('Error saving user data', [
                'userId' => $userData->getUserId(),
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to save user data', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Create new user data
     */
    public function create(string $userId): UserData
    {
        try {
            $userData = new UserData();
            $userData->setUserId($userId);
            return $userData;
        } catch (\Exception $e) {
            $this->logger->error('Error creating user data', [
                'userId' => $userId,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to create user data', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Find or create user data
     */
    public function findOrCreate(string $userId): UserData
    {
        try {
            $userData = $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);
            if (!$userData) {
                $userData = $this->create($userId);
                $this->mongoDBService->save($userData);
            }
            return $userData;
        } catch (\Exception $e) {
            $this->logger->error('Error finding or creating user data', [
                'userId' => $userId,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to find or create user data', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Get preferred dealer for user and brand
     */
    public function getPreferredDealer(string $userId, string $brand): ?array
    {
        try {
            $userData = $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);
            if (!$userData) {
                return null;
            }
            return $userData->getPreferredDealerForBrand($brand);
        } catch (\Exception $e) {
            $this->logger->error('Error getting preferred dealer', [
                'userId' => $userId,
                'brand' => $brand,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to get preferred dealer', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Set preferred dealer for user and brand
     */
    public function setPreferredDealer(string $userId, string $brand, array $dealerData): void
    {
        try {
            $userData = $this->findOrCreate($userId);
            $userData->setPreferredDealerForBrand($brand, $dealerData);
            $this->mongoDBService->save($userData);
        } catch (\Exception $e) {
            $this->logger->error('Error setting preferred dealer', [
                'userId' => $userId,
                'brand' => $brand,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to set preferred dealer', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }

    /**
     * Remove preferred dealer for user and brand
     */
    public function removePreferredDealer(string $userId, string $brand): void
    {
        try {
            $userData = $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);
            if ($userData) {
                $userData->removePreferredDealerForBrand($brand);
                $this->mongoDBService->save($userData);
            }
        } catch (\Exception $e) {
            $this->logger->error('Error removing preferred dealer', [
                'userId' => $userId,
                'brand' => $brand,
                'error' => $e->getMessage()
            ]);
            throw new RuntimeException('Failed to remove preferred dealer', Response::HTTP_SERVICE_UNAVAILABLE, $e);
        }
    }
}
