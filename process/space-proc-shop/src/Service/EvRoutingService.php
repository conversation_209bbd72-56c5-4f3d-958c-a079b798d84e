<?php

namespace App\Service;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use App\Connector\CustomHttpClient;
use App\Service\UserDataService;
use Webmozart\Assert\Assert;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;

class EvRoutingService
{
    use LoggerTrait;

    public function __construct(
        private UserDataService $userDataService,
        private CustomHttpClient $client,
        private string $boApiBaseUrl
    ) {
    }

    public function getLcdvByVin($vin)
    {
        try {
            // Use ODM service to get LCDV by VIN
            $lcdv = $this->userDataService->getLcdvByVin($vin);
            return $lcdv ?? '';
        } catch (\Exception $e) {
            $this->logger->error(
                sprintf('%s: Error getting LCDV by VIN', __METHOD__),
                [
                    'vin' => $vin,
                    'error' => $e->getMessage()
                ]
            );
            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    /**
     * Get BO EV Routing data
     * 
     * @param array $params Contains lcdv, dvq, dar, b0f, brand, country, language
     * @return array|null BO EV Routing data or null on error
     */
    public function getBoEvRouting($params) {
        try {
            $uri = $this->boApiBaseUrl . '/v1/ev_routing';
            $method = Request::METHOD_GET;

            $options = [
                'query' => [
                    'lcdv' => $params['lcdv'],
                    'dvq' => $params['dvq'],
                    'dar' => $params['dar'],
                    'b0f' => $params['b0f'],
                    'brand' => $params['brand'],
                    'country' => $params['country'],
                    'language' => $params['language']
                ],
            ];
            $this->logger->info(__METHOD__.':: '.$uri.' options '.json_encode($options));
            
            $response = $this->client->request($method, $uri, $options);
            if (Response::HTTP_OK === $response->getCode()) {
               return $response->getData()['data']?? null;
            }

            return null;
        } catch (\Exception $e) {
            $this->logger->error(
                sprintf('%s: Error fetching BO EV Routing data', __METHOD__),
                [
                    'params' => $params,
                    'error' => $e->getMessage()
                ]
            );
            return null;
        }
    }    
}