<?php

namespace App\Service;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use App\Model\VehicleModel;
use Symfony\Component\Serializer\SerializerInterface;
use Space\MongoDocuments\Service\MongoDBService;
use Space\MongoDocuments\Document\DrivingScore;
use Symfony\Component\HttpFoundation\Response;


/**
 * PHYD service.
 */
class PHYDService
{
    use LoggerTrait;

    private VehicleModel $vehicleModel;

    public const COLLECTION = 'drivingScore';

    public const FEATURE_CODE_PHYD = 'UBI_PHYD';

    public const FEATURE_CODE_STATUS_ENABLE = 'enable';
    public const FEATURE_CODE_STATUS_DISABLE = 'disable';

    public function __construct(
        private MongoDBService $mongoDBService,
        private SerializerInterface $serializer
    ) {
    }

    /**
     * PHYD - Getting Driving Score Data
     *
     * @param string $vin The Vehicle Identification Number (VIN) to filter by.
     * @param string|null $stliPolicyNumber (Optional) The STLI Policy Number to include in the filter.
     */
    public function getDrivingScore(string $vin, ?string $stliPolicyNumber = null): WSResponse
    {
        $this->logger->info('Getting driving score', [
            'vin' => $vin,
            'stliPolicyNumber' => $stliPolicyNumber,
        ]);

        try {
            $drivingScore = $this->mongoDBService->findOneBy(DrivingScore::class, ['vin' => $vin]);

            if ($drivingScore && (!$stliPolicyNumber || $drivingScore->getStliPolicyNumber() === $stliPolicyNumber)) {
                // Convert to array representation for backward compatibility
                $scoreArray = [
                    'vin' => $drivingScore->getVin(),
                    'stliPolicyNumber' => $drivingScore->getStliPolicyNumber(),
                    'globalScore' => $drivingScore->getGlobalScore(),
                    'overallScore' => $drivingScore->getOverallScore(),
                    'dailyScore' => $drivingScore->getDailyScore(),
                    'lastUpdate' => $drivingScore->getLastUpdate()?->format('Y-m-d\TH:i:s\Z')
                ];
                $data = [
                    'documents' => [$scoreArray]
                ];
                return new WSResponse(Response::HTTP_OK, json_encode($data));
            } else {
                return new WSResponse(Response::HTTP_NOT_FOUND, json_encode(['documents' => []]));
            }
        } catch (\Exception $e) {
            $this->logger->error('Error getting driving score', [
                'vin' => $vin,
                'stliPolicyNumber' => $stliPolicyNumber,
                'error' => $e->getMessage()
            ]);
            return new WSResponse(Response::HTTP_SERVICE_UNAVAILABLE, json_encode(['error' => $e->getMessage()]));
        }
    }

    /**
     * Constructs a MongoDB filter for fetching driving score data based on VIN
     * and optionally stliPolicyNumber.
     *
     * @param string|null $vin              The Vehicle Identification Number (VIN) to filter by.
     * @param string|null $stliPolicyNumber (Optional) The STLI Policy Number to filter by.
     */
    public function getDrivingScoreFilter(?string $vin, ?string $stliPolicyNumber = null)
    {
        $filter = [
            [
                '$match' => ['vin' => $vin],
            ],
        ];

        if (!empty($stliPolicyNumber)) {
            $filter[0]['$match']['stliPolicyNumber'] = $stliPolicyNumber;
        }
        return $filter;
    }
}
