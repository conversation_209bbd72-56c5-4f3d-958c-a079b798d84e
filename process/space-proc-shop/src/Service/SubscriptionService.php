<?php

namespace App\Service;

use Symfony\Component\HttpFoundation\Request;
use App\Connector\SysSamsDataConnector;
use App\Helper\WSResponse;
use App\Trait\LoggerTrait;

class SubscriptionService
{
    use LoggerTrait;

    public function __construct(
        private SysSamsDataConnector $sysSamsDataConnector
    ) {
    }

    /**
     * Get subscription data for a user and vehicle
     */
    public function getSubscription(string $userId, string $vin, string $target): WSResponse
    {
        $options = [
            'query' => [
                'userId' => $userId,
                'vin' => $vin,
                'target' => $target,
            ],
        ];
        $url = '/v1/subscription';
        $this->logger->info('=> '.__METHOD__." => Call API [$url] with options ", ['options' => $options]);
        
        return $this->sysSamsDataConnector->call(Request::METHOD_GET, $url, $options);
    }

    /**
     * Checkout cart items for subscription
     */
    public function cartItemsCheckout(
        string $userDbId,
        string $userId,
        string $vin,
        string $brand,
        string $country,
        string $language,
        string $source,
        string $target,
        array $requestBody
    ): WSResponse {
        $options = [
            'query' => [
                'userDbId' => $userDbId,
                'userId' => $userId,
                'vin' => $vin,
                'brand' => $brand,
                'country' => $country,
                'language' => $language,
                'source' => $source,
                'target' => $target,
            ],
            'json' => $requestBody,
        ];
        $url = '/v1/cart/checkout';
        $this->logger->info('=> '.__METHOD__." => Call API [$url] with options ", ['options' => $options]);
        
        return $this->sysSamsDataConnector->call(Request::METHOD_POST, $url, $options);
    }
}
