# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    tomtom_base_url: '%env(TOMTOM_URL)%'
    tomtom_api_key: '%env(TOMTOM_API_KEY)%'
    env(MONGODB_URL): ''
    env(MONGODB_DB): ''

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'



    App\Connector\SysSamsDataConnector:
        arguments:
            $url: "%env(MS_SYS_SAMS_DATA_URL)%"

    App\Connector\F2mConnector:
        arguments:
            $url: "%env(F2M_URL)%"
    
    App\Connector\SysAPDVConnector:
        arguments:
            $url: "%env(MS_SYS_APDV_URL)%"

    App\Connector\SysServiceAdvisorConnector:
        arguments:
            $url: "%env(MS_SYS_SERVICE_ADVISOR_URL)%"
    
    App\Service\TomTomService:
        arguments:
            $httpClient: '@http_client'
            $baseUrl: '%tomtom_base_url%'
            $apiKey: '%tomtom_api_key%'

    App\Service\CorvetSysConnector:
        arguments:
            $url: "%env(MS_SYS_CORVET_DATA_URL)%"

    App\Service\EvRoutingService:
        arguments:
            $userDataService: '@App\Service\UserDataService'
            $client: '@App\Connector\CustomHttpClient'
            $boApiBaseUrl: '%env(SPACE_BO_API_BASE_URL)%'

    # Space MongoDB Documents Service (following space-proc-me pattern)
    Space\MongoDocuments\Service\MongoDBService:
        arguments:
            $documentManager: '@doctrine_mongodb.odm.default_document_manager'
            $logger: '@logger'

    # Space MongoDB Documents Repository
    Space\MongoDocuments\Repository\SettingsRepository:
        factory: ['@doctrine_mongodb.odm.default_document_manager', 'getRepository']
        arguments:
            - 'Space\MongoDocuments\Document\Settings'

    # BoEvRoutingRepository
    Space\MongoDocuments\Repository\BoEvRoutingRepository:
        factory: ['@doctrine_mongodb.odm.default_document_manager', 'getRepository']
        arguments:
            - 'Space\MongoDocuments\Document\BoEvRouting'

    # UserDataService with MongoDB ODM (simple wrapper like space-proc-me)
    App\Service\UserDataService:
        arguments:
            $mongoDBService: '@Space\MongoDocuments\Service\MongoDBService'

    # SettingsService with SettingsRepository (like space-proc-me)
    App\Service\SettingsService:
        arguments:
            $settingsRepository: '@Space\MongoDocuments\Repository\SettingsRepository'
            $documentManager: '@doctrine_mongodb.odm.default_document_manager'

    # PHYDService with MongoDBService
    App\Service\PHYDService:
        arguments:
            $mongoDBService: '@Space\MongoDocuments\Service\MongoDBService'



    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
