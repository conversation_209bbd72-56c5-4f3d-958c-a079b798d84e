<?php

namespace App\Tests\Service;

use App\Helper\WSResponse;
use App\Service\PHYDService;
use Space\MongoDocuments\Service\MongoDBService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use Space\MongoDocuments\Document\DrivingScore;

class PHYDServiceTest extends TestCase
{
    private MongoDBService $mongoDBService;
    private LoggerInterface $logger;
    private PHYDService $PHYDService;
    private SerializerInterface $serializer;

    public function setUp(): void
    {
        $this->mongoDBService = $this->createMock(MongoDBService::class);
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->PHYDService = new PHYDService($this->mongoDBService, $this->serializer);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->PHYDService->setLogger($this->logger);
    }

    public function testGetDrivingScore(): void
    {
        $vin = 'VR3UPHNKSKT101603';
        $stliPolicyNumber = 'CN123321';

        // Create mock driving score document
        $drivingScore = $this->createMock(DrivingScore::class);
        $drivingScore->method('getStliPolicyNumber')->willReturn($stliPolicyNumber);
        $drivingScore->method('getVin')->willReturn($vin);
        $drivingScore->method('getGlobalScore')->willReturn(85);
        $drivingScore->method('getOverallScore')->willReturn(['value' => 40.15]);
        $drivingScore->method('getDailyScore')->willReturn(['value' => 38.5]);
        $drivingScore->method('getLastUpdate')->willReturn(new \DateTime('2023-01-01T12:00:00Z'));

        $this->mongoDBService
            ->expects($this->exactly(2))
            ->method('findOneBy')
            ->with(DrivingScore::class, ['vin' => $vin])
            ->willReturn($drivingScore);

        // Case without stliPolicyNumber
        $resultWithoutPolicy = $this->PHYDService->getDrivingScore($vin);
        $this->assertInstanceOf(WSResponse::class, $resultWithoutPolicy);
        $this->assertEquals(Response::HTTP_OK, $resultWithoutPolicy->getCode());

        // Case with stliPolicyNumber
        $resultWithPolicy = $this->PHYDService->getDrivingScore($vin, $stliPolicyNumber);
        $this->assertInstanceOf(WSResponse::class, $resultWithPolicy);
        $this->assertEquals(Response::HTTP_OK, $resultWithPolicy->getCode());
    }


    public function testGetDrivingScoreNotFound(): void
    {
        $vin = 'VR3UPHNKSKT101603';

        $this->mongoDBService
            ->expects($this->once())
            ->method('findOneBy')
            ->with(DrivingScore::class, ['vin' => $vin])
            ->willReturn(null);

        $result = $this->PHYDService->getDrivingScore($vin);
        $this->assertInstanceOf(WSResponse::class, $result);
        $this->assertEquals(Response::HTTP_NOT_FOUND, $result->getCode());
    }
}