<?php

namespace App\Tests\Manager;

use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;
use Psr\Log\LoggerInterface;

use App\Manager\UserManager;
use App\Service\UserDataService;
use App\Helper\WSResponse;
use Space\MongoDocuments\Document\UserData;

class UserManagerTest extends TestCase
{
    private LoggerInterface $logger;
    private UserDataService $userDataService;

    public function setUp(): void
    {
        $this->userDataService = $this->createMock(UserDataService::class);
    }
    public function testGetUsersBySessionId(): void
    {
        $serviceResult = new WSResponse(
            Response::HTTP_OK,
            '{"documents":[{"_id":"66b5eca07bbc4739adc88342","userId":"100000000f0b4dce874859657ae00100"}]}'
        );
        $this->userDataService->expects($this->once())
            ->method('getUsersBySessionId')
            ->willReturn($serviceResult);
        $userManager = new UserManager($this->userDataService);

        $this->logger = $this->createMock(LoggerInterface::class);
        $userManager->setLogger($this->logger);
        $users = $userManager->getUsersBySessionId('100000000f0b4dce874859657ae00100', 'VR3UPHNKSKT101603', '44dscsbf5d4dsdsdfsasa9657aefgdxc2');

        $this->assertEquals($serviceResult, $users);
    }

    public function testGetUserByUserId(): void
    {
        $serviceResult = new WSResponse(
            Response::HTTP_OK,
            '{"documents":[{"_id":"66b5eca07bbc4739adc88342","userId":"100000000f0b4dce874859657ae00100"}]}'
        );
        $this->userDataService->expects($this->once())
            ->method('getUserByUserId')
            ->willReturn($serviceResult);
        $userManager = new UserManager($this->userDataService);

        $this->logger = $this->createMock(LoggerInterface::class);
        $userManager->setLogger($this->logger);
        $user = $userManager->getUserByUserId('66b5eca07bbc4739adc88342');

        $this->assertEquals($serviceResult, $user);
    }
}
