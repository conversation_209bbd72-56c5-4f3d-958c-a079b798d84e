<?php

namespace Tests\Manager;

require __DIR__ . '/../../vendor/autoload.php';

use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\Response;

use App\Manager\CatalogManager;
use App\Service\ContribService;
use App\Connector\SysSamsDataConnector;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Model\CatalogModel;
use App\Manager\UserManager;
use App\Service\CorvetService;

class CatalogManagerTest extends TestCase
{
    private $catalogManager;
    private $contribService;
    private $sysSamsDataConnector;
    private $validator;
    private $logger;
    private $serializer;
    private $catalogModel;
    private $userManager;
    private $corvetService;

    protected function setUp(): void
    {
        $this->contribService = $this->createMock(ContribService::class);
        $this->sysSamsDataConnector = $this->createMock(SysSamsDataConnector::class);
        $this->userManager = $this->createMock(UserManager::class);
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->corvetService = $this->createMock(CorvetService::class);
        $this->catalogManager = new CatalogManager($this->sysSamsDataConnector, $this->serializer, $this->contribService, $this->validator, $this->userManager, $this->corvetService);
        $this->catalogManager->setLogger($this->logger);
        $this->catalogModel = $this->createMock(CatalogModel::class);
    }

    public function catalogOutput(): array
    {
        $data = [
            'success' => [
                [
                    "id" => "8adceeb38fbea01f018fc4bf967b0234",
                    "SKU" => "SKU-00000353",
                    "brand" => "FCA",
                    "productEntityId" => null,
                    "status" => "Activated",
                    "name" => "Connect One FCA",
                    "description" => "..",
                    "type" => "Bundle",
                    "rangeName" => null,
                    "rangeLevel" => null,
                    "rangeId" => null,
                    "discountId" => null,
                    "groupName" => "CONNECTONE",
                    "groupMemberType" => "Base Products",
                    "groupMemberLevel" => 1,
                    "isGroupDefault" => 1,
                    "ruleTag" => "AFTER_NCS_GENERIC,CONNECTONEFCA_NOT_SUBSCRIBED",
                    "effectiveStartDate" => "2010-01-01T00:00:00.000Z",
                    "effectiveEndDate" => "2100-01-01T00:00:00.000Z",
                    "maxWarrantyBeginDate" => null,
                    "minWarrantyBeginDate" => null,
                    "familyName" => "CONNECTONE",
                    "associationProcess" => "CVS",
                    "superGroup" => "CONNECTPACK",
                    "legalEntity" => "PSA Automobiles",
                    "canalActivation" => "PROMAN",
                    "error" => 0,
                    "linkedFamilyName" => null,
                    "code" => "ConnectOne_Code",
                    "bundleGroup" => null,
                    "bundleLevel" => null,
                    "standaloneProducts" => [
                        [
                            "id" => "8adca8879007e76801900c07e5a14833",
                            "SKU" => "SKU-000003451",
                            "brand" => "FCA",
                            "productEntityId" => null,
                            "status" => "Activated",
                            "name" => "My Assistant FCA",
                            "description" => "Basic service with emergency and breakdown call...",
                            "type" => "Commercial Service",
                            "rangeName" => null,
                            "rangeLevel" => null,
                            "rangeId" => null,
                            "discountId" => null,
                            "groupName" => "MYASSISTANT",
                            "groupMemberType" => "Base Products",
                            "groupMemberLevel" => 1,
                            "isGroupDefault" => 0,
                            "ruleTag" => "MyAssistant_NOT_SUBSCRIBED",
                            "effectiveStartDate" => "2010-01-01T00:00:00.000Z",
                            "effectiveEndDate" => "2100-12-31T00:00:00.000Z",
                            "maxWarrantyBeginDate" => null,
                            "minWarrantyBeginDate" => null,
                            "familyName" => "MYASSISTANT",
                            "associationProcess" => null,
                            "superGroup" => "MYASSISTANT",
                            "includedChannels" => null,
                            "excludedChannels" => null,
                            "legalEntity" => "PSA Automobiles",
                            "canalActivation" => "PROMAN",
                            "error" => 0,
                            "linkedFamilyName" => null,
                            "code" => "MyAssistant_Basic_Code",
                            "bundleGroup" => null,
                            "bundleLevel" => null,
                            "isMandatory" => 0,
                            "rpContextName" => "G10_FCABRANDS",
                            "functionalities" => [
                                [
                                    "id" => "8adcb7bd900c3f3f01900cbdd75513ea",
                                    "name" => "Vehicle Health Report",
                                    "description" => "Monthly report about the status of the vehicle's key systems and components.",
                                    "startValidityDate" => null,
                                    "endValidityDate" => null,
                                    "PSAFeatureCode" => "f_0003",
                                    "provider" => null,
                                    "creationDate" => "2024-06-18T07:16:18.000Z",
                                    "modificationDate" => null,
                                    "type" => "FDS",
                                    "isMandatory" => 1,
                                    "serviceIds" => ["SQDF", "VRC"]
                                ],
                                [
                                    "id" => "8adcb7bd900c3f3f01900cbe12b413f0",
                                    "name" => "In-Vehicle Notifications (Basic)",
                                    "description" => "Delivers and organizes vehicle related notifications to the customer, such as: recalls, maintenance, subscription/promos, registration.",
                                    "startValidityDate" => null,
                                    "endValidityDate" => null,
                                    "PSAFeatureCode" => "f_0053",
                                    "provider" => null,
                                    "creationDate" => "2024-06-18T07:16:18.000Z",
                                    "modificationDate" => null,
                                    "type" => "FDS",
                                    "isMandatory" => 1,
                                    "serviceIds" => ["IVM"]
                                ]
                            ],
                            "tnc" => [],
                            "services" => [],
                            "marketingProductSheetId" => null
                        ]
                    ],
                    "functionalities" => [],
                    "offers" => [
                        [
                            "id" => "8adceeb38fbea01f018fc4bf9f82026d",
                            "country" => "GB",
                            "target" => "B2C",
                            "name" => "Connect One FCA - Initial Trial - 10y",
                            "status" => "Active",
                            "pricingModel" => "One Off",
                            "duration" => "118",
                            "channel" => null,
                            "effectiveStartDate" => "2019-01-01T00:00:00.000Z",
                            "effectiveEndDate" => "2100-01-01T00:00:00.000Z",
                            "promoCode" => null,
                            "creationDate" => "2024-07-26T07:05:46.000Z",
                            "modificationDate" => null,
                            "ruleTag" => null,
                            "default" => 0,
                            "rscTags" => " ",
                            "description" => "",
                            "priority" => 1,
                            "isFreeTrial" => 0,
                            "freeTrialDuration" => null,
                            "freeTrialDurationType" => null,
                            "category" => "Basic",
                            "isRenewable" => 0,
                            "mileage" => null,
                            "fromPrice" => null,
                            "maxNumberVin" => null,
                            "privacyFunctionality" => null,
                            "durationType" => "WarrantyStartDate",
                            "isBundleOffer" => 1,
                            "rpContextName" => "G10_FCABRANDS",
                            "thirdPartyMappingId" => null,
                            "IFRS15" => null,
                            "prices" => [
                                [
                                    "id" => "8adcd55b8fbea02e018fc4c07dbd0a02+EUR",
                                    "currency" => "EUR",
                                    "name" => "Initial Trial",
                                    "price" => 0,
                                    "typeDiscount" => "FlatFee",
                                    "discountPercentage" => null,
                                    "discountAmount" => null,
                                    "periodType" => null,
                                    "costPercentage" => null,
                                    "POBIdentifier" => "REV - Monthly recognition over time"
                                ]
                            ],
                            "ratePlanPrice" => 0,
                            "unDiscountedRatePlanPrice" => 0
                        ]
                    ],
                    "tnc" => [],
                    "bundles" => [],
                    "services" => [],
                    "marketingProductSheetId" => null,
                    "associationLevel" => "LOW",
                    "associationJourney" => null
                ]
            ]
        ];
        return $data;
    }

    public function contribOutput()
    {
        $data = [
            'success' => [
                "homePage" => "https://ppr.services-store.dsautomobiles.co.uk/",
                "homePageSso" => "https://ppr.services-store.dsautomobiles.co.uk/login-redirect?redirect-url=https://ppr.services-store.dsautomobiles.co.uk/",
                "title" => "Telemaintenance",
                "fullDescription" => "<h4>LET DS&nbsp;LOOK AFTER YOUR SERVICE AND MAINTENANCE REMINDERS FOR YOU</h4>\n\n<p>You’ve got enough to think about, without having to remember when your vehicle needs servicing. &nbsp;</p>\n\n<p>With our intelligent Telemaintenance&nbsp;, DS&nbsp;continuously monitors the key functions of your vehicle and can let you know as soon as there is something that you need to get fixed.&nbsp;</p>\n\n<p>Telemaintenance&nbsp;is provided free of charge by DS&nbsp;for a minimum of 3 years, or for as long you own the vehicle and remain subscribed. &nbsp;Full information can be found in the<a data-entity-type=\"file\" data-entity-uuid=\"dabfd290-ed7f-4663-89f0-a217c1c45d62\" href=\"/sites/ds/files/uploaded-files/2102%20-%20DS%20Telemaintenance%20TsCs.pdf\" title=\" DS Telemaintenance TsCs.pdf \">&nbsp;</a><a data-entity-type=\"file\" data-entity-uuid=\"5ebfaca2-45dc-4503-a882-d0462a1233d9\" href=\"/sites/ds/files/uploaded-files/TC_Connect_One_UK.pdf\" title=\" DS Telemaintenance TsCs.pdf \"><u><strong>Telemaintenance&nbsp;Ts&amp;Cs</strong></u></a><a data-entity-type=\"file\" data-entity-uuid=\"dabfd290-ed7f-4663-89f0-a217c1c45d62\" href=\"/sites/ds/files/uploaded-files/2102%20-%20DS%20Telemaintenance%20TsCs.pdf\" title=\" DS Telemaintenance TsCs.pdf \">.&nbsp;</a></p>\n",
                "shortDescription" => "With Telemaintenance, DS lets you know if your vehicle requires scheduled maintenance or some extra attention.",
                "productUrl" => "https://ppr.services-store.dsautomobiles.co.uk/store/telemaintenance",
                "productUrlSso" => "https://ppr.services-store.dsautomobiles.co.uk/login-redirect?redirect-url=https://ppr.services-store.dsautomobiles.co.uk/store/telemaintenance",
                "productUrlCvs" => "https://ppr.services-store.dsautomobiles.co.uk/login-redirect?xcsrf=[VIN]&jwt=[TOKEN_CVS]&inboundApplication=[Mymark]&redirect-url=https://ppr.services-store.dsautomobiles.co.uk/store/telemaintenance",
                "marketingProductSheetId" => "35",
                "productId" => "8adc8f99680e4ebd016819a43e56517b",
                "features" => [
                    [
                        "desc" => "<p>Real time maintenance alerts to look after your vehicle and to always know the next service needed</p>\r\n",
                        "icon" => "https://ppr.services-store.dsautomobiles.co.uk/sites/ds/files/2024-05/TMTS%20%28big%29.png",
                        "full_desc" => "<p><strong>What you need to know, when you need to know</strong><br />\r\nDS intelligent maintenance features are your car’s personal assistant: the vehicle's key functions are constantly monitored and you are alerted if anything needs your attention.&nbsp;<br />\r\nWe also remind of your next service, making it easy to book an appointment in seconds.</p>\r\n"
                    ]
                ],
                "topMainImage" => "https://ppr.services-store.dsautomobiles.co.uk/sites/ds/files/styles/service_slider_element_desktop/public/2024-12/5-Telemaintenance-350x244.jpg",
                "tnc" => [
                    "version" => "3.2",
                    "title" => "T&C TM/TS",
                    "id" => "36",
                    "tncUrl" => "https://ppr.services-store.dsautomobiles.co.uk/sites/ds/files/2024-05/TC_Connect_One_UK.pdf"
                ],
                "legals" => [],
                "consents" => [
                    [
                        "category" => null,
                        "title" => "Geolocation Consents",
                        "shortDescription" => "I understand and accept that the provision of this(theses) service(s) requires vehicle geolocation and data will only be proceeded in accordance with the privacy policy.",
                        "longConsentDesc" => null,
                        "tnc" => [
                            "version" => "3.2",
                            "title" => "T&C TM/TS",
                            "id" => "36",
                            "tncUrl" => "https://ppr.services-store.dsautomobiles.co.uk/sites/ds/files/2024-05/TC_Connect_One_UK.pdf"
                        ],
                        "data" => null,
                        "purpose" => null,
                        "consumer" => null
                    ]
                ]
            ]
        ];
        return $data;
    }

    public function testGetCatalog()
    {
        $params = [
            'brand' => 'AL',
            'country' => 'IT',
            'language' => 'it',
            'userId' => 'ACNT200000328091',
            'vin' => 'VR1URHNSSKW013788',
            'source' => 'app',
        ];
        $userData = new WSResponse(
            Response::HTTP_OK,
             '{"documents":[{"_id":"66b5eca07bbc4739adc88342","userId":"100000000f0b4dce874859657ae00100","userDbId":"100000000f0b4dce874859657ae00100"}]}'
        );
        $this->userManager->expects($this->once())
            ->method('getUserByUserId')
            ->willReturn($userData);
        $this->sysSamsDataConnector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(200, $this->catalogOutput()));
        $this->contribService->method('getContrib')->willReturn(new WSResponse(200, $this->contribOutput()));
        $this->corvetService->method('getCorvetResponse')->willReturn(['VEHICULE' => ['LISTE_ATTRIBUTES_7' => ['ATTRIBUT' => []]]]);
        $this->corvetService->method('getManagedAttributes')->willReturn([]);

        $response = $this->catalogManager->getCatalog($params);
        $this->assertInstanceOf(SuccessResponse::class, $response);
    }

    public function testGetCatalogWithInvalidData()
    {
        $params = [
            'brand' => 'AL',
            'country' => 'IT',
            'language' => 'it',
            'userId' => 'ACNT200000328091',
            'vin' => 'VR1URHNSSKW013788',
            'source' => 'app',
        ];
        $userData = new WSResponse(
            Response::HTTP_OK,
             '{"documents":[{"_id":"66b5eca07bbc4739adc88342","userId":"100000000f0b4dce874859657ae00100","userDbId":"100000000f0b4dce874859657ae00100"}]}'
        );
        $this->userManager->expects($this->once())
            ->method('getUserByUserId')
            ->willReturn($userData);
        $this->sysSamsDataConnector->expects($this->once())
            ->method('call')
            ->willReturn(new WSResponse(200, ['data'=>[]]));
        $this->contribService->method('getContrib')->willReturn(new WSResponse(200, $this->contribOutput()));

        $response = $this->catalogManager->getCatalog($params);
        $this->assertInstanceOf(ErrorResponse::class, $response);
    }


}
