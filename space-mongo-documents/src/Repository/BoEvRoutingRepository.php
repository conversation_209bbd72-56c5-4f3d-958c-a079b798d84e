<?php

namespace Space\MongoDocuments\Repository;

use Doctrine\ODM\MongoDB\Repository\DocumentRepository;
use Space\MongoDocuments\Document\BoEvRouting;

class BoEvRoutingRepository extends DocumentRepository
{
    /**
     * Find BO EV Routing data by vehicle parameters
     */
    public function findByVehicleParameters(string $lcdv, string $dvq, string $dar, string $b0f, string $brand, string $country, string $language): ?BoEvRouting
    {
        return $this->findOneBy([
            'lcdv' => $lcdv,
            'dvq' => $dvq,
            'dar' => $dar,
            'b0f' => $b0f,
            'brand' => $brand,
            'country' => $country,
            'language' => $language
        ]);
    }

    /**
     * Find BO EV Routing data by LCDV
     */
    public function findByLcdv(string $lcdv): array
    {
        return $this->findBy(['lcdv' => $lcdv]);
    }

    /**
     * Find enabled BO EV Routing configurations
     */
    public function findEnabled(): array
    {
        return $this->findBy(['enabled' => true]);
    }

    /**
     * Find by brand and country
     */
    public function findByBrandAndCountry(string $brand, string $country): array
    {
        return $this->findBy([
            'brand' => $brand,
            'country' => $country
        ]);
    }
}
