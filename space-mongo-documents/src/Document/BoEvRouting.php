<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Space\MongoDocuments\Repository\BoEvRoutingRepository;

#[MongoDB\Document(collection: 'boEvRouting', repositoryClass: BoEvRoutingRepository::class)]
class BoEvRouting
{
    #[MongoDB\Id]
    #[Serializer\SerializedName("_id")]
    private ?string $id = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $lcdv = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $dvq = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $dar = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $b0f = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $brand = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $country = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $language = null;

    #[MongoDB\Field(type: 'bool')]
    private ?bool $enabled = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $label = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $constantSpeedConsumptionInkWhPerHundredkm = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $engineType = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $maxChargeInkWh = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $vehicleMaxSpeed = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $vehicleWeight = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $vehicleAxleWeight = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $vehicleLength = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $vehicleWidth = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $vehicleHeight = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $accelerationEfficiency = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $decelerationEfficiency = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $uphillEfficiency = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $downhillEfficiency = null;

    #[MongoDB\Field(type: 'hash')]
    private array $chargingCurveArray = [];

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getLcdv(): ?string
    {
        return $this->lcdv;
    }

    public function setLcdv(?string $lcdv): self
    {
        $this->lcdv = $lcdv;
        return $this;
    }

    public function getDvq(): ?string
    {
        return $this->dvq;
    }

    public function setDvq(?string $dvq): self
    {
        $this->dvq = $dvq;
        return $this;
    }

    public function getDar(): ?string
    {
        return $this->dar;
    }

    public function setDar(?string $dar): self
    {
        $this->dar = $dar;
        return $this;
    }

    public function getB0f(): ?string
    {
        return $this->b0f;
    }

    public function setB0f(?string $b0f): self
    {
        $this->b0f = $b0f;
        return $this;
    }

    public function getBrand(): ?string
    {
        return $this->brand;
    }

    public function setBrand(?string $brand): self
    {
        $this->brand = $brand;
        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): self
    {
        $this->country = $country;
        return $this;
    }

    public function getLanguage(): ?string
    {
        return $this->language;
    }

    public function setLanguage(?string $language): self
    {
        $this->language = $language;
        return $this;
    }

    public function getEnabled(): ?bool
    {
        return $this->enabled;
    }

    public function setEnabled(?bool $enabled): self
    {
        $this->enabled = $enabled;
        return $this;
    }

    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function setLabel(?string $label): self
    {
        $this->label = $label;
        return $this;
    }

    public function getConstantSpeedConsumptionInkWhPerHundredkm(): ?string
    {
        return $this->constantSpeedConsumptionInkWhPerHundredkm;
    }

    public function setConstantSpeedConsumptionInkWhPerHundredkm(?string $constantSpeedConsumptionInkWhPerHundredkm): self
    {
        $this->constantSpeedConsumptionInkWhPerHundredkm = $constantSpeedConsumptionInkWhPerHundredkm;
        return $this;
    }

    public function getEngineType(): ?string
    {
        return $this->engineType;
    }

    public function setEngineType(?string $engineType): self
    {
        $this->engineType = $engineType;
        return $this;
    }

    public function getMaxChargeInkWh(): ?string
    {
        return $this->maxChargeInkWh;
    }

    public function setMaxChargeInkWh(?string $maxChargeInkWh): self
    {
        $this->maxChargeInkWh = $maxChargeInkWh;
        return $this;
    }

    public function getVehicleMaxSpeed(): ?string
    {
        return $this->vehicleMaxSpeed;
    }

    public function setVehicleMaxSpeed(?string $vehicleMaxSpeed): self
    {
        $this->vehicleMaxSpeed = $vehicleMaxSpeed;
        return $this;
    }

    public function getVehicleWeight(): ?string
    {
        return $this->vehicleWeight;
    }

    public function setVehicleWeight(?string $vehicleWeight): self
    {
        $this->vehicleWeight = $vehicleWeight;
        return $this;
    }

    public function getVehicleAxleWeight(): ?string
    {
        return $this->vehicleAxleWeight;
    }

    public function setVehicleAxleWeight(?string $vehicleAxleWeight): self
    {
        $this->vehicleAxleWeight = $vehicleAxleWeight;
        return $this;
    }

    public function getVehicleLength(): ?string
    {
        return $this->vehicleLength;
    }

    public function setVehicleLength(?string $vehicleLength): self
    {
        $this->vehicleLength = $vehicleLength;
        return $this;
    }

    public function getVehicleWidth(): ?string
    {
        return $this->vehicleWidth;
    }

    public function setVehicleWidth(?string $vehicleWidth): self
    {
        $this->vehicleWidth = $vehicleWidth;
        return $this;
    }

    public function getVehicleHeight(): ?string
    {
        return $this->vehicleHeight;
    }

    public function setVehicleHeight(?string $vehicleHeight): self
    {
        $this->vehicleHeight = $vehicleHeight;
        return $this;
    }

    public function getAccelerationEfficiency(): ?string
    {
        return $this->accelerationEfficiency;
    }

    public function setAccelerationEfficiency(?string $accelerationEfficiency): self
    {
        $this->accelerationEfficiency = $accelerationEfficiency;
        return $this;
    }

    public function getDecelerationEfficiency(): ?string
    {
        return $this->decelerationEfficiency;
    }

    public function setDecelerationEfficiency(?string $decelerationEfficiency): self
    {
        $this->decelerationEfficiency = $decelerationEfficiency;
        return $this;
    }

    public function getUphillEfficiency(): ?string
    {
        return $this->uphillEfficiency;
    }

    public function setUphillEfficiency(?string $uphillEfficiency): self
    {
        $this->uphillEfficiency = $uphillEfficiency;
        return $this;
    }

    public function getDownhillEfficiency(): ?string
    {
        return $this->downhillEfficiency;
    }

    public function setDownhillEfficiency(?string $downhillEfficiency): self
    {
        $this->downhillEfficiency = $downhillEfficiency;
        return $this;
    }

    public function getChargingCurveArray(): array
    {
        return $this->chargingCurveArray;
    }

    public function setChargingCurveArray(array $chargingCurveArray): self
    {
        $this->chargingCurveArray = $chargingCurveArray;
        return $this;
    }
}
